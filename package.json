{"name": "dashboard-demo", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "lucide-react": "^0.532.0", "mapbox-gl": "^3.14.0", "maplibre-gl": "^4.7.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-map-gl": "^7.1.9", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "recharts": "^3.1.0", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/mapbox-gl": "^3.4.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-slick": "^0.23.13", "typescript": "^4.9.5"}}