// EmailInput.tsx
import React from 'react';

interface EmailInputProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

export default function EmailInput({ label, value, onChange, placeholder }: EmailInputProps) {
  return (
    <div className="config-input">
      <label>{label}</label>
      <input
        type="email"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
    </div>
  );
}
