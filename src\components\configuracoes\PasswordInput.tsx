// PasswordInput.tsx
import React from 'react';

interface PasswordInputProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

export default function PasswordInput({ label, value, onChange, placeholder }: PasswordInputProps) {
  return (
    <div className="config-input">
      <label>{label}</label>
      <input
        type="password"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
    </div>
  );
}
