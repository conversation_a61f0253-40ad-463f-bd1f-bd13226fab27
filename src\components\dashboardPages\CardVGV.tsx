import React from 'react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YA<PERSON><PERSON>,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';
import '../../styles/dashboardPagesStyle/CardVGV.css';

const data = [
  { mes: 'Jan', Vgv: 120000 },
  { mes: 'Fev', Vgv: 95000 },
  { mes: 'Mar', Vgv: 105000 },
  { mes: 'Abr', Vgv: 130000 },
  { mes: 'Mai', Vgv: 115000 },
  { mes: 'Jun', Vgv: 140000 },
  { mes: 'Jul', Vgv: 125000 },
  { mes: 'Ago', Vgv: 150000 },
  { mes: 'Set', Vgv: 170000 },
  { mes: 'Out', Vgv: 160000 },
  { mes: 'Nov', Vgv: 180000 },
  { mes: 'Dez', Vgv: 200000 },
];

const CardVGV: React.FC = () => {
  return (
    <div className="card-vgv">
      <h3 className="card-vgv-title">VGV <PERSON> (R$)</h3>
      <ResponsiveContainer width="100%" height={180}>
        <LineChart data={data}>
          <CartesianGrid stroke="#f0f0f0" />
          <XAxis dataKey="mes" />
          <YAxis tickFormatter={(value) => `R$ ${value / 1000}k`} />
          <Tooltip formatter={(value: number) => `R$ ${value.toLocaleString('pt-BR')}`} />
          <Line
            type="monotone"
            dataKey="Vgv"
            stroke="#29306A"
            strokeWidth={2}
            dot={{ r: 3, stroke: '#29306A', strokeWidth: 1, fill: '#fff' }}
            activeDot={{ r: 5 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CardVGV;
