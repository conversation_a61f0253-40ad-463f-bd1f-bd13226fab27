import React from 'react';
import '../../../styles/dashboardPagesStyle/Charts.css';

interface EstoqueDataItem {
  cidade: string;
  vendidos: number;
  disponiveis: number;
}

const estoqueData: EstoqueDataItem[] = [
  { cidade: 'Recife', vendidos: 40, disponiveis: 45 },
  { cidade: 'Olinda', vendidos: 28, disponiveis: 29 },
  { cidade: 'Ilha de Itamaracá', vendidos: 18, disponiveis: 18 },
  { cidade: 'São Lourenço da Mata', vendidos: 25, disponiveis: 25 },
  { cidade: 'Jaboat<PERSON>', vendidos: 30, disponiveis: 15 },
  { cidade: 'Camaragibe', vendidos: 20, disponiveis: 10 },
  { cidade: 'Rio Formoso', vendidos: 45, disponiveis: 70 },
  { cidade: 'Gravatá', vendidos: 35, disponiveis: 11 },
  { cidade: 'Tamandaré', vendidos: 20, disponiveis: 34 },
];

export default function EstoqueCidadeChart() {
  return (
    <div className="card-grafico-cidade">
      <h3>Estoque por Localidade</h3>

      <div className="tabela-cidade">
        <div className="tabela-header">
          <span>#</span>
          <span>Localidade</span>
          <span>Vendas sobre o total</span>
          <span>Disponíveis</span>
        </div>

        <div className="tabela-body">
          {estoqueData.map((item, index) => {
            const total = item.vendidos + item.disponiveis;
            const vendidosPercent = (item.vendidos / total) * 100;
            const disponiveisPercent = (item.disponiveis / total) * 100;
            const vendidosLabel = `${Math.round(vendidosPercent)}%`;

            const tooltipText = `Vendidos: ${item.vendidos} | Disponíveis: ${item.disponiveis}`;

            return (
              <div className="linha-cidade" key={index}>
                <span>{String(index + 1).padStart(2, '0')}</span>
                <span>{item.cidade}</span>

                <div className="barra-vendas-wrapper tooltip-wrapper">
                  <div className="barra-vendas">
                    <div
                      className="vendidos"
                      style={{ width: `${vendidosPercent}%` }}
                    />
                    <div
                      className="disponiveis"
                      style={{ width: `${disponiveisPercent}%` }}
                    />
                  </div>
                  <span className="porcentagem-label">{vendidosLabel}</span>
                  <span className="tooltip">{tooltipText}</span>
                </div>

                <span className="badge">{item.disponiveis}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
