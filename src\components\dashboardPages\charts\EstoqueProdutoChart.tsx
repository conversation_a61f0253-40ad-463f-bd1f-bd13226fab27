import React from 'react';
import '../../../styles/dashboardPagesStyle/Charts.css';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface ProdutoDataItem {
  name: string;
  value: number;
}

const produtoData: ProdutoDataItem[] = [
  { name: '<PERSON><PERSON>', value: 50 },
  { name: 'Apartamento', value: 80 },
  { name: 'Casa', value: 100 },
  { name: 'Comercial', value: 80 },
  { name: 'Compacto', value: 40 },
];

const cores: string[] = ['#29306A', '#F55EEB', '#32BEF0', '#0633FB', '#00C49F', '#FFBB28', '#FF8042'];

export default function EstoqueProdutoChart() {
  return (
    <div className="card-grafico-produto">
      <h3>Estoque por Tipo de Produto</h3>
      <ResponsiveContainer width="100%" height={180}>
        <PieChart>
          <Pie
            data={produtoData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={60}
          >
            {produtoData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={cores[index % cores.length]} />
            ))}
          </Pie>
          <Legend verticalAlign="bottom" height={30} iconType="circle" />
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
