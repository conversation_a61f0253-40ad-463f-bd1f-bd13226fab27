import React from 'react';
import '../../../styles/dashboardPagesStyle/Charts.css';
import { BarChart, Bar, XAxis, YA<PERSON>s, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface TipologiaDataItem {
  name: string;
  value: number;
}

const tipologiaData: TipologiaDataItem[] = [
  { name: '1Q', value: 158 },
  { name: '2Q', value: 120 },
  { name: '3Q', value: 270 },
  { name: '4Q ou +', value: 87 },
];

export default function EstoqueTipologiaChart() {
  return (
    <div className="card-grafico-tipologia">
      <h3>Estoque por Tipologia</h3>
      <p className="subtitulo-grafico">Distribuição por número de quartos (Casas e Apartamentos)</p>
      <ResponsiveContainer width="100%" height={185}>
        <BarChart
          data={tipologiaData}
          layout="vertical" // torna o gráfico lateral
        >
          <XAxis type="number" />
          <YAxis dataKey="name" type="category" />
          <Tooltip />
          <Legend verticalAlign="bottom" height={20} iconType="circle" />
          <Bar dataKey="value" fill="#29306A" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
