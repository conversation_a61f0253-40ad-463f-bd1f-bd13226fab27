import React from 'react';
import '../../../styles/dashboardPagesStyle/Charts.css';
import { Line<PERSON>hart, Line, XAxis, <PERSON>A<PERSON><PERSON>, Toolt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

interface VendasDataItem {
  mes: string;
  Vigente: number;
  Anterior: number;
}

const dataVendas: VendasDataItem[] = [
  { mes: 'Jan', Vigente: 40, Anterior: 60 },
  { mes: 'Fev', Vigente: 50, Anterior: 70 },
  { mes: 'Mar', Vigente: 45, Anterior: 80 },
  { mes: 'Abr', Vigente: 60, Anterior: 65 },
  { mes: 'Mai', Vigente: 55, Anterior: 85 },
  { mes: 'Jun', Vigente: 58, Anterior: 90 },
];

export default function EvolucaoVendasChart() {
  return (
    <div className="card-grafico-evolucao">
      <h3>Evolução de Vendas</h3>
      <ResponsiveContainer width="100%" height={155}>
        <LineChart data={dataVendas}>
          <XAxis dataKey="mes" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="Vigente" stroke="#29306A" strokeWidth={2} />
          <Line type="monotone" dataKey="Anterior" stroke="#F55EEB" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
