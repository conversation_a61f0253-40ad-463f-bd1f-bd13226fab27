import React from 'react';
import GaugeRadialCustom from './VelocidadeVendasGauge';
import '../../../styles/dashboardPagesStyle/Charts.css';

interface GaugeCardProps {
  title: string;
  value: number;
}

const GaugeCard: React.FC<GaugeCardProps> = ({ title, value }) => {
  const meta = 1.0;
  const percentual = Math.min((value / meta) * 100, 100);

  return (
    <div className="gauge-card">
      <div className="gauge-header">
        <div className="gauge-title">{title}</div>
      </div>

      <div className="gauge-content">
        <GaugeRadialCustom value={value} />
      </div>
    </div>
  );
};

export default GaugeCard;
