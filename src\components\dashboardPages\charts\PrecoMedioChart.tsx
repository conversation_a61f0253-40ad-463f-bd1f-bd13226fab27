import React from 'react';
import '../../../styles/dashboardPagesStyle/Charts.css';
import { BarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface PrecoMedioDataItem {
  cidade: string;
  Privativo: number;
  Construido: number;
}

const data: PrecoMedioDataItem[] = [
  { cidade: 'Recife', Privativo: 6500, Construido: 7200 },
  { cidade: 'Olinda', Privativo: 5800, Construido: 6700 },
  { cidade: 'Paulista', Privativo: 5000, Construido: 6100 },
  { cidade: 'Igarassu', Privativo: 4700, Construido: 5900 },
  { cidade: 'Itapissuma', Privativo: 4300, Construido: 5400 },
];

export default function PrecoMedioChart() {
  return (
    <div className="card-grafico-preco">
      <h3>Preço Médio do m²</h3>
      <ResponsiveContainer width="100%" height={155}>
        <BarChart data={data}>
          <XAxis dataKey="cidade" />
          <YAxis />
          <Tooltip />
          <Legend verticalAlign="bottom" height={20} iconType="circle" />
          <Bar dataKey="Privativo" fill="#29306A" />
          <Bar dataKey="Construido" fill="#F55EEB" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
