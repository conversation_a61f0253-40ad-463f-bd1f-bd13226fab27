import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

interface GaugeProps {
  value: number; // de 0 a 100
  size?: number;
}

const GaugeRadialCustom: React.FC<GaugeProps> = ({ value, size = 110 }) => {
  const cx = size / 2;
  const cy = size / 2;
  const clampedValue = Math.min(Math.max(value, 0), 100);
  const angle = 180 * (clampedValue / 100); // ângulo de 0 a 180

  const RADIAN = Math.PI / 180;
  const needleLength = size / 2.5;

  // Cálculo correto para ângulo no sentido horário (de 180 até 0)
  const needleAngle = 180 - angle;
  const needleX = cx + needleLength * Math.cos(needleAngle * RADIAN);
  const needleY = cy - needleLength * Math.sin(needleAngle * RADIAN); 

  const data = [
    { value: clampedValue },
    { value: 100 - clampedValue },
  ];

  const COLORS = ['#29306A', '#ddd'];

  return (
    <div style={{ width: size , height: size * 0.75 }}>
      <PieChart width={size * 1.1} height={size}>
        <Pie
          data={data}
          startAngle={180}
          endAngle={0}
          cx={cx}
          cy={cy}
          innerRadius={size / 2.5}
          outerRadius={size / 2}
          dataKey="value"
          paddingAngle={0}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index]} />
          ))}
        </Pie>

        {/* Ponteiro */}
        <g>
          <line
            x1={cx}
            y1={cy}
            x2={needleX}
            y2={needleY}
            stroke="#F55EEB"
            strokeWidth={3}
          />
          <circle cx={cx} cy={cy} r={5} fill="#F55EEB" />
        </g>

        {/* Texto */}
        <text
          x={cx}
          y={cy + size * 0.1}
          textAnchor="middle"
          dominantBaseline="central"
          fontSize={15}
          fill="#29306A"
          fontWeight="bold"
        >
          {Math.round(clampedValue)}%
        </text>
      </PieChart>
    </div>
  );
};

export default GaugeRadialCustom;
