import React from 'react';
import CardEmpreendimento from './CardEmpreendimento';
import '../../styles/empreendimentosStyle/ListaEmpreendimentos.css';
import type { Filtros } from './Filtros';

interface Empreendimento {
  id: number;
  titulo: string;
  preco: string;
  localizacao: string;
  imagem: string;
  quartos: number;
  suites: number;
  metragem: number;
}

interface ListaEmpreendimentosProps {
  filtros: Filtros;
}

const empreendimentos: Empreendimento[] = [
  {
    id: 1,
    titulo: 'Apartamento 3 quartos - Boa Viagem',
    preco: '2.300/mês',
    localizacao: 'Recife - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
  {
    id: 2,
    titulo: 'Casa ampla com garagem',
    preco: '1.800/mês',
    localizacao: 'Olinda - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
  {
    id: 3,
    titulo: 'Duplex',
    preco: '1.500/mês',
    localizacao: 'Recife - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
  {
    id: 4,
    titulo: 'Casa Pequena',
    preco: '1.300/mês',
    localizacao: 'Jaboatão dos Guararapes - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
  {
    id: 5,
    titulo: 'Apartamento 2 Quartos - Graças',
    preco: '2.000/mês',
    localizacao: 'Recife - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
  {
    id: 6,
    titulo: 'Casa ampla com garagem',
    preco: '1.800/mês',
    localizacao: 'Olinda - PE',
    imagem: '/apartamento.jpg',
    quartos: 3,
    suites: 2,
    metragem: 85,
  },
];

export default function ListaEmpreendimentos({ filtros }: ListaEmpreendimentosProps) {
  // Certifique-se que filtros.pesquisa, estado e municipio são strings não vazias
  const pesquisaLower = filtros.pesquisa.trim().toLowerCase();
  const estadoLower = filtros.estado.trim().toLowerCase();
  const municipioLower = filtros.municipio.trim().toLowerCase();

  const filtrados = empreendimentos.filter((item) => {
    const tituloLower = item.titulo.toLowerCase();
    const localizacaoLower = item.localizacao.toLowerCase();

    // Verifica se título contém a pesquisa
    const tituloMatch = pesquisaLower === '' || tituloLower.includes(pesquisaLower);

    // Verifica estado (exemplo simples, pois localizacao tem 'Cidade - Estado')
    const estadoMatch = estadoLower === '' || localizacaoLower.includes(estadoLower);

    // Verifica município (também simplificado)
    const municipioMatch = municipioLower === '' || localizacaoLower.includes(municipioLower);

    return tituloMatch && estadoMatch && municipioMatch;
  });

  return (
    <div className="lista-empreendimentos">
      {filtrados.map((item) => (
        <CardEmpreendimento key={item.id} {...item} />
      ))}
    </div>
  );
}
