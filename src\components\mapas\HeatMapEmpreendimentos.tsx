// HeatMapEmpreendimentos.tsx
import React, { useState, useEffect } from 'react';
import Map, { Source, Layer } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import type { LayerProps } from 'react-map-gl';

interface HeatMapEmpreendimentosProps {
  filtros: any; // ajuste o tipo conforme seus filtros
  initialViewState?: {
    latitude: number;
    longitude: number;
    zoom: number;
  };
}

// Camada do mapa que "pinta" os setores pelo atributo qtdEmpreendimentos
const choroplethLayer: LayerProps = {
  id: 'setores-choropleth',
  type: 'fill',
  source: 'setores',
  paint: {
    'fill-color': [
      'interpolate',
      ['linear'],
      ['get', 'qtdEmpreendimentos'],
      0, '#f2f0f7',
      5, '#cbc9e2',
      10, '#9e9ac8',
      20, '#756bb1',
      30, '#54278f'
    ],
    'fill-opacity': 0.7,
    'fill-outline-color': '#333'
  }
};

export default function HeatMapEmpreendimentos({ filtros, initialViewState }: HeatMapEmpreendimentosProps) {
  const [viewState, setViewState] = useState({
    latitude: initialViewState?.latitude || -8.05,
    longitude: initialViewState?.longitude || -34.9,
    zoom: initialViewState?.zoom || 11,
  });

  const [originalGeojson, setOriginalGeojson] = useState<any>(null);
  const [geojson, setGeojson] = useState<any>(null);

  // Simulação dos dados de empreendimentos por setor (você deve pegar do backend/fonte real)
  // Aqui você poderia usar filtros para buscar dados reais
  const [dadosEmpreendimentos, setDadosEmpreendimentos] = useState<{ setorId: string, qtd: number }[]>([]);

  useEffect(() => {
    // 1. Carrega o GeoJSON original (com setores censitários)
    fetch('/data/pernambuco_setores.geojson')
      .then(res => res.json())
      .then(data => {
        setOriginalGeojson(data);
      })
      .catch(console.error);
  }, []);

  useEffect(() => {
    if (!originalGeojson) return;

    // 2. Simula dados de empreendimentos (substitua pelo fetch real com filtros)
    // Exemplo: dadosEmpreendimentos = [{ setorId: '123', qtd: 15 }, ...]
    // Aqui você pode aplicar seus filtros para buscar dados filtrados
    // Vou deixar um mock genérico:
    const mockDados = [
      { setorId: 'setor_1', qtd: 15 },
      { setorId: 'setor_2', qtd: 5 },
      { setorId: 'setor_3', qtd: 20 },
      // ...
    ];
    setDadosEmpreendimentos(mockDados);
  }, [originalGeojson, filtros]);

  useEffect(() => {
    if (!originalGeojson) return;
    if (!dadosEmpreendimentos.length) return;

    // 3. Atualiza o GeoJSON para incluir qtdEmpreendimentos em properties de cada setor
    const updatedGeojson = {
      ...originalGeojson,
      features: originalGeojson.features.map((feature: any) => {
        // Aqui identifica o setor pelo ID (ajuste para seu dado)
        const setorId = feature.properties['id_setor'] || feature.properties['setor_id'];

        // Procura quantidade no dadosEmpreendimentos
        const encontrado = dadosEmpreendimentos.find(d => d.setorId === setorId);

        // Atualiza qtdEmpreendimentos
        return {
          ...feature,
          properties: {
            ...feature.properties,
            qtdEmpreendimentos: encontrado ? encontrado.qtd : 0
          }
        };
      })
    };

    setGeojson(updatedGeojson);
  }, [dadosEmpreendimentos, originalGeojson]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Map
        {...viewState}
        mapboxAccessToken="pk.eyJ1IjoiZXZhbmRzb25jZXNhciIsImEiOiJjbWN3YXNtMjkwMGl2Mm5wd21mdGRlazA0In0.bvgSecEoO0w-PDKVfkcpjQ"
        mapStyle="mapbox://styles/mapbox/light-v10"
        onMove={evt => setViewState(evt.viewState)}
        style={{ width: '100%', height: '100%' }}
      >
        {geojson && (
          <Source id="setores" type="geojson" data={geojson}>
            <Layer {...choroplethLayer} />
          </Source>
        )}
      </Map>
    </div>
  );
}
