import React, { useState, useEffect } from 'react';
import Map, { Source, Layer, Marker } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { MapPin } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import HeatMapEmpreendimentos from './HeatMapEmpreendimentos'; 

interface Regiao {
  estado: string;
  cidade: string;
  bairro: string;
}

interface Imovel {
  id: string;
  latitude: number;
  longitude: number;
  titulo: string;
  tipo?: string;
  bairro?: string;
  quartos?: number;
  construtora?: string;
  preco?: number;
  unidadesTotais?: number;
  vendidosPercent?: number;
  disponivel?: boolean;
}

interface MapaEmpreendimentosProps {
  filtros: any;
  regiao?: Regiao[];
}

const MapaEmpreendimentos: React.FC<MapaEmpreendimentosProps> = ({ filtros, regiao }) => {
  const [viewState, setViewState] = useState({
    latitude: -8.05,
    longitude: -34.9,
    zoom: 11,
  });

  const [imoveis, setImoveis] = useState<Imovel[]>([]);
  const [heatmapOn, setHeatmapOn] = useState(false);
  const [imovelSelecionado, setImovelSelecionado] = useState<Imovel | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const mockImoveis: Imovel[] = [
      { id: '1', latitude: -8.05, longitude: -34.90, titulo: 'Condomínio Pinheiros', tipo: 'Apartamento', bairro: 'Pinheiros', quartos: 2, construtora: 'Construtora Theta', preco: 680000, unidadesTotais: 180, vendidosPercent: 38, disponivel: true },
      { id: '2', latitude: -8.06, longitude: -34.92, titulo: 'Imóvel 2' },
      { id: '3', latitude: -8.03, longitude: -34.88, titulo: 'Imóvel 3' },
    ];
    setImoveis(mockImoveis);
  }, []);

  return (
    <div style={{ height: '80vh', width: '100%', position: 'relative' }}>
      <div style={{ position: 'absolute', top: 10, right: 30, zIndex: 10, background: 'white', padding: 10, borderRadius: 12 }}>
        <label>
          <input
            type="checkbox"
            checked={heatmapOn}
            onChange={() => setHeatmapOn(!heatmapOn)}
          /> Mostrar Heatmap
        </label>
      </div>

      {heatmapOn ? (
        <HeatMapEmpreendimentos filtros={filtros} initialViewState={viewState} />
      ) : (
        
        <div
          style={{
            height: '100%',
            width: '100%',
            maxWidth: '98%',
            marginLeft: 'auto',
            marginRight: 'auto',
            border: '1px solid #ccc',
            borderRadius: '12px',
            overflow: 'hidden',
            boxSizing: 'border-box',
          }}
        >
          <Map
            {...viewState}
            mapboxAccessToken="pk.eyJ1IjoiZXZhbmRzb25jZXNhciIsImEiOiJjbWN3YXNtMjkwMGl2Mm5wd21mdGRlazA0In0.bvgSecEoO0w-PDKVfkcpjQ"
            mapStyle="mapbox://styles/mapbox/streets-v11"
            onMove={evt => setViewState(evt.viewState)}
            style={{ width: '100%', height: '100%' }}
          >
            <Source
              id="imoveis"
              type="geojson"
              data={{
                type: 'FeatureCollection',
                features: imoveis.map(imovel => ({
                  type: 'Feature',
                  properties: { id: imovel.id, title: imovel.titulo },
                  geometry: { type: 'Point', coordinates: [imovel.longitude, imovel.latitude] },
                })),
              }}
            />

            {imoveis.map(imovel => (
              <Marker
                key={imovel.id}
                longitude={imovel.longitude}
                latitude={imovel.latitude}
                anchor="bottom"
              >
                <div
                  title={imovel.titulo}
                  style={{ display: 'inline-block', cursor: 'pointer' }}
                  onClick={() => setImovelSelecionado(imovel)}
                >
                  <MapPin
                    size={22}
                    color="white"
                    style={{ border: '2px solid #29306a', borderRadius: '50%', backgroundColor: '#29306a', padding: '2px' }}
                  />
                </div>
              </Marker>
            ))}
          </Map>
        </div>
      )}

      {imovelSelecionado && (
        <div
          style={{
            position: 'absolute',
            left: 20,
            top: 50,
            background: 'white',
            borderRadius: 10,
            boxShadow: '0 3px 6px rgba(0,0,0,0.1)',
            padding: 20,
            width: 300,
            zIndex: 20,
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
            <div>
              <h3 style={{ margin: 0, color: '#00225a' }}>{imovelSelecionado.titulo}</h3>
              <span style={{ fontWeight: 'bold', backgroundColor: '#44c3ff', color: 'white', padding: '4px 10px', borderRadius: 12, fontSize: 12 }}>
                {imovelSelecionado.disponivel ? 'Disponível' : 'Indisponível'}
              </span>
            </div>
            <button
              onClick={() => setImovelSelecionado(null)}
              style={{
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                fontSize: 18,
                color: '#666',
                fontWeight: 'bold',
                lineHeight: 1,
              }}
              aria-label="Fechar"
            >
              ×
            </button>
          </div>

          <ul style={{ listStyle: 'none', padding: 0, margin: 0, color: '#444', fontSize: 14 }}>
            <li><strong>Tipo:</strong> {imovelSelecionado.tipo || '—'}</li>
            <li><strong>Bairro:</strong> {imovelSelecionado.bairro || '—'}</li>
            <li><strong>Quartos:</strong> {imovelSelecionado.quartos ?? '—'}</li>
            <li><strong>Construtora:</strong> {imovelSelecionado.construtora || '—'}</li>
          </ul>

          <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between', color: '#444', fontSize: 14 }}>
            <div><strong>R$ {imovelSelecionado.preco?.toLocaleString() || '—'}</strong></div>
            <div>{imovelSelecionado.vendidosPercent ? `${imovelSelecionado.vendidosPercent}% vendido` : '—'}</div>
          </div>

          <small style={{ color: '#999' }}>
            {imovelSelecionado.unidadesTotais ? `${imovelSelecionado.unidadesTotais} unidades totais` : ''}
          </small>

          <div style={{ marginTop: 8, height: 8, background: '#dcdcdc', borderRadius: 4 }}>
            <div
              style={{
                width: `${imovelSelecionado.vendidosPercent || 0}%`,
                height: '100%',
                backgroundColor: '#44c3ff',
                borderRadius: 4,
                transition: 'width 0.3s ease',
              }}
            />
          </div>

          <button
            onClick={() => navigate(`/empreendimentos/${imovelSelecionado.id}`)}
            style={{
              marginTop: 20,
              width: '100%',
              padding: '10px',
              backgroundColor: '#29306a',
              color: 'white',
              border: 'none',
              borderRadius: 6,
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: 16,
            }}
          >
            Ir para empreendimento
          </button>
        </div>
      )}
    </div>
  );
};

export default MapaEmpreendimentos;
