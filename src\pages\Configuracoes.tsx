import Sidebar from '../components/SideBar';
import '../styles/configuracoesStyle/Configuracoes.css';
import { User } from 'lucide-react';

import TextInput from '../components/configuracoes/TextInput';
import PasswordInput from '../components/configuracoes/PasswordInput';
import EmailInput from '../components/configuracoes/EmailInput';
import NumberInput from '../components/configuracoes/NumberInput';
import DateInput from '../components/configuracoes/DateInput';
import Button from '../components/configuracoes/Button';
import SubmitButton from '../components/configuracoes/SubmitButton';

import React, { useState, ChangeEvent, FormEvent } from 'react';

export default function Configuracoes() {
  const [nome, setNome] = useState<string>('');
  const [senha, setSenha] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [idade, setIdade] = useState<string>('');
  const [dataNasc, setDataNasc] = useState<string>('');

  const auditData = {
    criadoEm: '15/01/2024 às 14:30',
    ultimaModificacao: '07/01/2025 às 16:45',
    modificadoPor: 'João Silva Santos',
    status: 'Ativo'
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    alert(`Dados salvos:\nNome: ${nome}\nEmail: ${email}\nIdade: ${idade}\nNascimento: ${dataNasc}`);
  };

  return (
    <div style={{ display: 'flex', background: '#f5f5f5' }}>
      <Sidebar />
      <div className="config-container">
        <h2>Configurações</h2>
        <p>Gerencie as suas configurações de cadastro interno.</p>

        <div className="config-card"> 
          <div className="card-header">
            <User className="card-icon" />
            <h3 className="card-title">Informações Pessoais</h3>
          </div>

          <form onSubmit={handleSubmit}>
            {/* Linha 1 - Nome */}
            <div className="form-row">
              <TextInput
                label="Nome"
                value={nome}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setNome(e.target.value)}
                placeholder="Digite seu nome"
              />
            </div>

            {/* Linha 2 - Senha + Email */}
            <div className="form-row">
              <EmailInput
                label="E-mail"
                value={email}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
                placeholder="Digite seu e-mail"
              />
              <PasswordInput
                label="Senha"
                value={senha}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setSenha(e.target.value)}
                placeholder="Digite sua senha"
              />
            </div>

            {/* Linha 3 - Data de nascimento e Idade */}
            <div className="form-row">
              <NumberInput
                label="Idade"
                value={idade}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setIdade(e.target.value)}
                placeholder="Digite sua idade"
              />
              <DateInput
                label="Data de Nascimento"
                value={dataNasc}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setDataNasc(e.target.value)}
              />
            </div>

            {/* Botões */}
            <div className="form-buttons">
              <SubmitButton label="Salvar" />
              <Button label="Cancelar" onClick={() => alert('Alterações canceladas.')} />
            </div>
          </form>
          
          {/* Informações de Auditoria */}
          <hr className="divider" />
          <div className="audit-info">
            <div className="audit-title">Informações de Auditoria</div>

            <div className="audit-grid">
              <div>
                <div className="audit-label">Criado em:</div>
                <div className="audit-value">{auditData.criadoEm}</div>
              </div>

              <div>
                <div className="audit-label">Última modificação:</div>
                <div className="audit-value">{auditData.ultimaModificacao}</div>
              </div>

              <div>
                <div className="audit-label">Modificado por:</div>
                <div className="audit-value">{auditData.modificadoPor}</div>
              </div>

              <div>
                <div className="audit-label">Status:</div>
                <div 
                  className={`audit-status ${
                    auditData.status.toLowerCase() === 'ativo' ? 'ativo' : 'inativo'
                  }`}
                >
                  {auditData.status}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
