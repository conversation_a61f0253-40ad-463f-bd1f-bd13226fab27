/* Formulário */
.config-container {
  flex: 1;
  padding: 20px;
  max-width: auto;
  margin: 0 auto;
  height: 100%;
  min-height: 100vh;
}

.config-container h2 {
  font-size: 1.8rem;
  color: rgba(41, 48, 106);
  margin-bottom: 0.1rem;
}

.config-container p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

.config-container h3 {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.config-input {
  display: flex;
  flex-direction: column;
}

.config-input label {
  margin-bottom: 0.4rem;
  font-weight: 500;
  color: #333;
}

.config-input input {
  padding: 8px 12px;
  font-size: 1rem;
  border: 1px solid #29306A;
  border-radius: 10px;
}

.config-button {
  padding: 10px 18px;
  font-size: 1rem;
  background-color: #29306A;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 1rem;
}

.config-button:hover {
  background-color: #1d235a;
}

.config-card {
  background-color: white;
  padding: 24px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.form-row .config-input {
  flex: 1;
  min-width: 250px;
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
}

.card-icon {
  font-size: 30px;
  color: #29306A;
  margin-right: 25px;
}

.card-title {
  font-size: 1.4rem;
  font-weight: bold;
  color: #29306A;
  margin: 0;
}

/* Informações de Auditoria */
.divider {
  margin: 30px 0 20px;
  border: none;
  border-top: 1px solid #e0e0e0;
}

.audit-info {
  background-color: #f9f9fb;
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
}

.audit-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 16px;
  font-weight: bold;
  color: #29306A;
}

.audit-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  font-size: 0.95rem;
  color: #333;
}

.audit-label {
  font-weight: 500;
  color: #555;
}

.audit-value {
  margin-top: 4px;
}

.audit-status {
  margin-top: 4px;
  font-weight: 600;
  color: green;
}

.audit-status.ativo {
  color: green;
}

.audit-status.inativo {
  color: red;
}