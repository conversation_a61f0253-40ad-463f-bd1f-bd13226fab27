/* CardEmpreendimento.css */
.card-link {
  text-decoration: none;
  color: inherit;
}

.card-container {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 16px;
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-imagem {
  width: 40%;
  height: auto;
  object-fit: cover;
}

.card-infos {
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-icones {
  display: flex;
  gap: 12px;
  margin: 8px 0;
  font-size: 14px;
  color: #555;
}

.card-icones span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-preco {
  font-weight: bold;
  color: #29306a;
}
