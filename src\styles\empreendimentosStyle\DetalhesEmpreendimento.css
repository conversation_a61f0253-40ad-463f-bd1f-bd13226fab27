.detalhes-container {
  max-width: 1000px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  color: #29306a;
}

.loading {
  text-align: center;
  margin-top: 40px;
  font-size: 18px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-bottom: 24px;
}

.titulo {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
  justify-content: center;
  text-align: center;
}

.localizacao {
  font-size: 16px;
  color: #777;
  justify-content: center;
  text-align: center;
}

.preco {
  font-size: 24px;
  font-weight: bold;
}

.galeria {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 45px;
  justify-content: center;
}

.foto {
  width: calc(33.33% - 8px);
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.resumo {
  margin-bottom: 45px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 16px;
  text-align: center;
}

.resumo svg {
  margin-top: 0px;
  margin-bottom: 10px;
}

.resumo p {
  margin-top: 10px;
  margin-bottom: 0px;
}

.resumo div {
  background-color: #29306a;
  color: #fff;
  padding: 12px 85px;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.caracteristicas {
  display: flex;
  justify-content: center;
  gap: 5%;
  margin-bottom: 40px;
  text-align: center;
}

.caracteristica-box {
  flex: 1;
  max-width: 250px;
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

}

.caracteristica-box h3 {
  margin-top: 10px;
  margin-bottom: 10px;
  color: #29306a;
  font-size: 18px;
}

.caracteristica-box ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.caracteristica-box li {
  margin-bottom: 8px;
  color: #444;
  font-size: 14px;
}

.mapa {
  margin: 32px 0;
}

.sobre-imovel {
  margin-bottom: 32px;
  padding: 15px 10%;
  text-align: center;
  justify-content: center;
}

.sobre {
  line-height: 1.6;
  margin-top: 8px;
  text-align: center;
  color: #444;
}

.comodidades {
  list-style: disc;
  margin-left: 20px;
  margin-bottom: 32px;
  color: #444;
}

.slider {
  width: 80%;
  margin: 20px 0;
}

.foto-slider {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
}


.slick-prev:before,
.slick-next:before {
  color: #29306a; 
  font-size: 30px; 
}

.slick-dots li button:before {
  color: #29306a; 
}

.slick-dots li.slick-active button:before {
  color: #29306a; 
}

