.filtros-mapa-card {
  background-color: #fff;
  padding: 16px;
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.linha-filtros {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.linha-dropdowns {
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  width: 100%; 
}

.row-ranges {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}

.slider-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  flex: 1;
}

.range-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr); 
  gap: 8px;
  min-width: 180px;
  flex: 1;
}

.range-group > :first-child {
  grid-column: 1 / -1; 
}

input[type="number"],
select {
  padding: 6px;
  border-radius: 6px;
  border: 1px solid #29306a;
  width: 100%;
  box-sizing: border-box;
  color: #29306a;
}

input[type="number"]::placeholder,
select {
  color: #29306a;
}

input[type="number"]:focus,
select:focus {
  border-color: #1c224d;
  outline: none;
  box-shadow: 0 0 3px rgba(41, 48, 106, 0.4);
}

input[type="number"]::placeholder,
select {
  color: #29306a;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
}

.collapse-group {
  width: 100%;
  margin-top: 8px;
}

/* Título do collapse e dos sliders */
.collapse-header,
.range-group label,
.slider-group label {
  color: #29306a; 
  font-size: 16px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  user-select: none;
}

.range-group label {
  color: #29306a;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  user-select: none;
}


.botao-container {
  margin-top: 20px;
  width: auto;
  text-align: center;
}

.botao-filtrar {
  padding: 12px;
  background-color: #29306a;
  height: auto;
  width: 100%;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.botao-filtrar:hover {
  background-color: #1c224d;
}

/* Slider */

.slider-group {
  color: #1a237e; /* Texto azul escuro */
}

/* Slider estilizado com alinhamento corrigido */
input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #ccd6f6;
  outline: none;
  margin-top: 8px;
  cursor: pointer;
  position: relative;
}

input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(
    to right,
    #29306a 0%,
    #29306a var(--range-progress, 50%),
    #ccd6f6 var(--range-progress, 50%),
    #ccd6f6 100%
  );
  outline: none;
  margin-top: 8px;
  cursor: pointer;
  position: relative;
  z-index: 0;
}

/* Chrome/Safari Thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #29306a;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(41, 48, 106, 0.7);
  margin-top: -2px; /* centraliza o thumb na track */
  position: relative;
  z-index: 1;
}

/* Firefox Thumb */
input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #29306a;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(41, 48, 106, 0.7);
}

/* Firefox Track */
input[type="range"]::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: #ccd6f6;
}

/* Slider label styling */
.slider-group label {
  font-weight: bold;
  color: #29306a;
  margin-bottom: 4px;
}

.filtros-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #29306a;
  font-weight: bold;
  user-select: none;
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: background-color 0.3s ease;
}

.filtros-toggle:hover {
  background-color: #c5d0ff;
}

