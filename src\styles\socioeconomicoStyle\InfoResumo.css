.info-resumo-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap; 
  gap: 15px;
}

.info-box {
  position: relative;
  flex: 1 0 25%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.info-text h4 {
  font-size: 1.2rem;
  color: #29306A;
  margin-bottom: 0.1rem;
}

.info-text p {
  font-size: 1.3rem;
  font-weight: bold;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-circle {
  width: 40px;
  height: 40px;
  background-color: #29306A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #ffffff;
}

.info-tooltip-wrapper {
  position: absolute;
  top: 8px;
  right: 8px;
  cursor: pointer;
  display: inline-block;
}

.custom-tooltip {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-90%);
  background-color: #29306A;
  color: #fff;
  padding: 6px 10px;
  border-radius: 5px;
  white-space: nowrap;
  font-size: 0.8rem;
  z-index: 10;
  opacity: 0.9;
  pointer-events: none; /* evita que o tooltip atrapalhe o mouse */
}

.custom-tooltip::after {
  content: "";
  position: absolute;
  top: 100%; /* seta o triângulo embaixo do tooltip */
  left: 90%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: #29306A transparent transparent transparent;
}

.info-tooltip:hover {
  opacity: 0.8;
}

.info-icon-top{
  color: #29306A;
}